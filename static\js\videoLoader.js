/**
 * Video Loader Module
 * Handles loading and displaying YouTube videos
 */

/**
 * Video data configuration
 */
const videoData = [
  {
    title: "OVER-THINKING",
    videoId: "Ct5kE8KGnQM",
    url: "https://www.youtube.com/watch?v=Ct5kE8KGnQM",
  },
  {
    title: "Childhood Nostalgia",
    videoId: "2jfeauEQx7w",
    url: "https://www.youtube.com/watch?v=2jfeauEQx7w",
  },
  {
    title: "This Perfect World",
    videoId: "kFSdn2X1Ttw",
    url: "https://www.youtube.com/watch?v=kFSdn2X1Ttw",
  },
  {
    title: "Tragic Ending",
    videoId: "uf6PZ9WisZQ",
    url: "https://www.youtube.com/watch?v=uf6PZ9WisZQ",
  },
];

/**
 * Creates a video element with iframe and link
 * @param {Object} video - Video object containing title, videoId, and url
 * @returns {HTMLElement} - The created video element
 */
function createVideoElement(video) {
  const colDiv = document.createElement("div");
  colDiv.className = "col";

  colDiv.innerHTML = `
    <iframe
      src="https://www.youtube.com/embed/${video.videoId}?si=dca-S4qIp2txXlSA"
      title="${video.title}"
      frameborder="0"
      width="560"
      height="315"
      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture; web-share"
      referrerpolicy="strict-origin-when-cross-origin"
      allowfullscreen
    ></iframe>
    <div class="video-links">
      <a
        rel="noopener"
        class="link"
        href="${video.url}"
        target="_blank"
      >
        <strong>${video.title}</strong>
      </a>
    </div>
  `;

  return colDiv;
}

/**
 * Loads and displays all videos in the video container
 * @returns {void}
 */
function loadVideos() {
  const videoContainer = document.getElementById("videoContainer");

  if (!videoContainer) {
    console.warn("videoContainer não encontrado.");
    return;
  }

  videoData.forEach((video) => {
    const videoElement = createVideoElement(video);
    videoContainer.appendChild(videoElement);
  });
}

/**
 * Gets the video data array
 * @returns {Array} - Array of video objects
 */
function getVideoData() {
  return videoData;
}

/**
 * Adds a new video to the collection
 * @param {Object} video - Video object containing title, videoId, and url
 */
function addVideo(video) {
  if (video && video.title && video.videoId && video.url) {
    videoData.push(video);
  } else {
    console.error("Invalid video object provided");
  }
}

// Export functions for module usage
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    loadVideos,
    getVideoData,
    addVideo,
    createVideoElement
  };
}
