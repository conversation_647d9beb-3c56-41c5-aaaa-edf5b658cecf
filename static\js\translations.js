/**
 * Advanced Translation System
 * Supports multiple languages with loading indicators, caching, and error handling
 * Default language: pt-BR (Portuguese Brazil)
 * Supported languages: en-US, pt-BR, jp-JP, ru-RU, hi-IN, zh-CN
 */

// Language configuration
const languageConfig = {
  defaultLanguage: 'pt-BR',
  fallbackLanguage: 'en-US',
  supportedLanguages: ['en-US', 'pt-BR', 'jp-JP', 'ru-RU', 'hi-IN', 'zh-CN'],
  cacheEnabled: true,
  loadingIndicator: true
};

// Translation cache
const translationCache = new Map();

// Loading state
let isLoadingTranslations = false;

const translations = {
  'en-US': {
    // Meta information
    languageName: "English",
    languageCode: "en-US",
    direction: "ltr",

    // Navigation and basic elements
    ishiuba: "iSHIUBA",
    videos: "Videos",
    about: "About",
    greeting: "Hello, world!",
    mainMessage:
      "The website is still under construction, but you can check out some of my videos, my about page, <br> or my social networks that are present in the website's footer.",
    highlight: "Highlights",
    footer: "All rights reserved.",
    videosTitle: "Videos",
    videosDescription:
      "This is the video section, here are some videos to watch:",
    aboutTitle: "About",
    aboutMessage:
      "I made this website for fun. Hope you enjoy coz I'm about to make it better and better!<br/> Thank you for visiting!",
    Homepage: "Homepage",
    Videos: "Videos",
    About: "About",

    // Loading and error messages
    loading: "Loading...",
    error: "Error",
    tryAgain: "Try Again",
    languageChanged: "Language changed to English",

    // Video section
    watchOnYoutube: "Watch on YouTube",
    videoLoadError: "Error loading video",

    // Theme
    lightTheme: "Light Theme",
    darkTheme: "Dark Theme",
    themeChanged: "Theme changed"
  },
  'pt-BR': {
    // Meta information
    languageName: "Português (Brasil)",
    languageCode: "pt-BR",
    direction: "ltr",

    // Navigation and basic elements
    ishiuba: "iSHIUBA",
    videos: "Vídeos",
    about: "Sobre",
    greeting: "Olá, mundo!",
    mainMessage:
      "O site ainda está em construção, mas você pode conferir alguns dos meus vídeos, minha página sobre <br> ou minhas redes sociais que estão presentes no rodapé do site.",
    highlight: "Destaques",
    footer: "Todos os direitos reservados.",
    videosTitle: "Vídeos",
    videosDescription:
      "Esta é a seção de vídeos, aqui estão alguns vídeos para assistir:",
    aboutTitle: "Sobre",
    aboutMessage:
      "Eu fiz este site por diversão. Espero que você goste, pois estou prestes a melhorá-lo cada vez mais!<br/> Obrigado pela visita!",
    Homepage: "Página Inicial",
    Videos: "Vídeos",
    About: "Sobre",

    // Loading and error messages
    loading: "Carregando...",
    error: "Erro",
    tryAgain: "Tentar Novamente",
    languageChanged: "Idioma alterado para Português (Brasil)",

    // Video section
    watchOnYoutube: "Assistir no YouTube",
    videoLoadError: "Erro ao carregar vídeo",

    // Theme
    lightTheme: "Tema Claro",
    darkTheme: "Tema Escuro",
    themeChanged: "Tema alterado"
  },
  'jp-JP': {
    // Meta information
    languageName: "日本語",
    languageCode: "jp-JP",
    direction: "ltr",

    // Navigation and basic elements
    ishiuba: "アイシウバ",
    videos: "ビデオ",
    about: "概要",
    greeting: "こんにちは、世界！",
    mainMessage:
      "ウェブサイトはまだ建設中ですが、いくつかのビデオ、概要ページ、<br>またはフッターにあるソーシャルネットワークをチェックできます。",
    highlight: "ハイライト",
    footer: "全著作権所有。",
    videosTitle: "ビデオ",
    videosDescription:
      "これはビデオセクションです。ここにいくつかの視聴するビデオがあります:",
    aboutTitle: "概要",
    aboutMessage:
      "私はこのウェブサイトを楽しみのために作りました。気に入ってもらえると嬉しいです。<br/>どんどん改善していくつもりです！訪問してくれてありがとうございます！",
    Homepage: "ホームページ",
    Videos: "ビデオ",
    About: "概要",

    // Loading and error messages
    loading: "読み込み中...",
    error: "エラー",
    tryAgain: "再試行",
    languageChanged: "言語が日本語に変更されました",

    // Video section
    watchOnYoutube: "YouTubeで視聴",
    videoLoadError: "ビデオの読み込みエラー",

    // Theme
    lightTheme: "ライトテーマ",
    darkTheme: "ダークテーマ",
    themeChanged: "テーマが変更されました"
  },

  'ru-RU': {
    // Meta information
    languageName: "Русский",
    languageCode: "ru-RU",
    direction: "ltr",

    // Navigation and basic elements
    ishiuba: "iSHIUBA",
    videos: "Видео",
    about: "О нас",
    greeting: "Привет, мир!",
    mainMessage:
      "Веб-сайт все еще находится в разработке, но вы можете посмотреть некоторые из моих видео, страницу обо мне, <br> или мои социальные сети, которые находятся в нижнем колонтитуле сайта.",
    highlight: "Основные моменты",
    footer: "Все права защищены.",
    videosTitle: "Видео",
    videosDescription:
      "Это раздел видео, вот несколько видео для просмотра:",
    aboutTitle: "О нас",
    aboutMessage:
      "Я создал этот сайт для развлечения. Надеюсь, вам понравится, потому что я собираюсь делать его все лучше и лучше!<br/> Спасибо за визит!",
    Homepage: "Главная",
    Videos: "Видео",
    About: "О нас",

    // Loading and error messages
    loading: "Загрузка...",
    error: "Ошибка",
    tryAgain: "Попробовать снова",
    languageChanged: "Язык изменен на русский",

    // Video section
    watchOnYoutube: "Смотреть на YouTube",
    videoLoadError: "Ошибка загрузки видео",

    // Theme
    lightTheme: "Светлая тема",
    darkTheme: "Темная тема",
    themeChanged: "Тема изменена"
  },
